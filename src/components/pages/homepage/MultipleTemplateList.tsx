import { useMemo } from 'react'
import { toCDNImage } from '@/utils'
import { TemplateListProps } from './const'
import classNames from 'classnames'
import SingleTemplate from './SingleTemplate'
import { useTranslation } from 'react-i18next'
import { ThemeDetail } from '@/apis/types'

/** 多行模版列表 - 简单的n行两列布局 */
const MultipleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  activeGender,
}: TemplateListProps) => {
  const { t } = useTranslation()

  // 将模板列表按两列分组
  const templateRows = useMemo(() => {
    if (!selectTemplateList?.length) return []

    const rows: ThemeDetail[][] = []
    for (let i = 0; i < selectTemplateList.length; i += 2) {
      rows.push(selectTemplateList.slice(i, i + 2))
    }
    return rows
  }, [selectTemplateList])

  return (
    <div className="relative py-6 top-[50%] -translate-y-[50%]">
      {templateRows.length > 0 ? (
        <div className="space-y-6">
          {templateRows.map((row, rowIndex) => (
            <div key={rowIndex} className="flex justify-center items-center space-x-12 px-12">
              {row.map((template, colIndex) => (
                <div key={template.id} className="w-[38.88vw] ipad:w-[22rem]">
                  <SingleTemplate
                    isMultiple
                    activeGender={activeGender}
                    item={template}
                    active={template.id === activeTemplate?.id}
                    onSelect={() => setActiveTemplate(template)}
                  />
                </div>
              ))}
            </div>
          ))}
        </div>
      ) : (
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 my-[34px] p-4 text-center">
          <img
            className="w-24"
            src={toCDNImage('/images/common/grinning.png')}
            alt=""
          />
          <div className="text-xl font-bold maze-primary-text mt-6">
            {t('当前分类下还没有模板')}
            <br />
            {t('去看看别的分类吧')}
          </div>
        </div>
      )}
    </div>
  )
}
export default MultipleTemplateList
