import styles from '../camera-common.module.css'
// import PostureTip from '../MazePostureTip'
import { useAtom, useAtomValue } from 'jotai'
import {
  taskTypeAtom,
  drawPayOrderAtom,
  screenOrientationAtom,
  shotNumAtom,
  imageNumAtom,
  userUploadImageAtom,
  machineInfoAtom,
  isPortraitCameraEnableAtom,
  mazeDrawPayOrderAtom,
  modelsPerGenerationCountAtom,
  selectedEventDetailAtom,
  selectedThemeDetailAtom,
  selectedGenderAtom,
} from '@/stores'
import { Button } from '@/components/ui/shad/button'
import { useEffect, useMemo, useRef, useState } from 'react'
import { CameraCurrentStatus, useCamera } from '@/hooks/useCamera'
import classNames from 'classnames'
import { uploadQiNiu } from '@/apis/upload-oss'
import {
  AiTaskType,
  FitNumber,
  MirrorSexEnum,
  OssBucket,
  TaskCreateType,
  useCheckImageMutation,
} from '@/graphqls/types'
import { useNavigate } from 'react-router-dom'
import { useAiTask } from '../useAiTask'
import CustomPhotoModule from '../CustomPhotoModule'
import {
  graphQLErrorMessage,
  isIPad,
  isMachine,
  isMirror,
  isPad,
  randomRange,
  sleep,
} from '@/utils'
import { useToast } from '@/components/ui/shad/use-toast'
import { useDebounce } from '@/hooks/useDebounce'
import { usePreciseCountDown } from '@/hooks/usePreciseCountDown'
// import { PhotoBorder } from '../PhotoBorder'
import { PhotoMask } from '../PhotoMask'
import { RoleMask } from '../RoleMask'
import { useBridge } from '@/hooks/useBridge'
import { CameraTypeEnum, CanonActionEnum } from '@/stores/types'
import { useTranslation } from 'react-i18next'

import { publicPreLoadSourceObj } from '@/configs/source'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { useDevice } from '@/hooks/useDevice'

const VIDEO_ID = 'cameraVideoInput'
const CANVAS_ID = 'cameraCanvasInput'

/** 拍照状态 */
enum PhotoStatus {
  /** 预览 */
  preview = 'preview',
  /** 倒计时 */
  countDown = 'countDown',
  /** 重试 */
  retry = 'retry',
}
/** 拍摄倒计时 */
const SHOT_COUNT_DOWN = 5
const randomKey = randomRange(32, 32)

const PhotoHasCamera = () => {
  const [status, setStatus] = useState(PhotoStatus.preview)
  const [createLoading, setCreateLoading] = useState(false)
  const blobImgRef = useRef<Blob | null>(null)
  /** 已上传的七牛链接 */
  const photoUrlRef = useRef<string>('')
  const [shotNum] = useAtom(shotNumAtom)
  const [imageNum] = useAtom(imageNumAtom)
  const [drawPayOrder] = useAtom(drawPayOrderAtom)
  const [mazeDrawPayOrder] = useAtom(mazeDrawPayOrderAtom)
  const [modelsPerGenerationCount] = useAtom(modelsPerGenerationCountAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [checkImgAction] = useCheckImageMutation()
  const [remainShotTime, setRemainShotTime] = useState(shotNum)
  const [image] = useAtom(userUploadImageAtom)
  const isPortraitCameraEnable = useAtomValue(isPortraitCameraEnableAtom)
  const [createType, setCreateType] = useState<TaskCreateType>(
    TaskCreateType.SHOOT
  )
  const [taskType] = useAtom(taskTypeAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)
  const [selectedThemeDetail] = useAtom(selectedThemeDetailAtom)
  const [selectedGender] = useAtom(selectedGenderAtom)
  const [uploadKey, setUploadKey] = useState(randomKey)
  const [qrcodeOpen, setQrcodeOpen] = useState(false)

  const navigator = useNavigate()
  const { getDefaultDeviceInfo } = useDevice()
  const { takeCannonLight } = useBridge()
  const { createAiTask } = useAiTask()
  const { toast } = useToast()
  const { t } = useTranslation()

  const {
    cameraCurrentStatus,
    initCamera,
    handleCatchPhoto,
    changeVideoStatus,
    closeCamera,
    realVideoSize,
  } = useCamera({
    videoId: VIDEO_ID,
    canvasId: CANVAS_ID,
  })

  const finishShotCamera = async () => {
    setStatus(PhotoStatus.retry)
    // 倒计时结束拍摄，摄像头画面暂停
    const blobImg = (await handleCatchPhoto()) as Blob
    blobImgRef.current = blobImg
  }
  const { second, startSecond, resetSecond } = usePreciseCountDown(
    SHOT_COUNT_DOWN,
    async () => {
      console.log('cameraType', machineInfo.cameraType, 'isMirror', isMirror())
      if (isMirror() && machineInfo.cameraType === CameraTypeEnum.CANON) {
        // 佳能相机模式
        takeCannonLight({
          callback: async ({ action }: { action: CanonActionEnum }) => {
            console.log('takeCannonLight-action:', action)
            switch (action) {
              case CanonActionEnum.PRESS:
                console.log('按下快门时间')
                break
              case CanonActionEnum.RELEASE:
                console.log('佳能相机快门释放完成，开始截屏')
                await sleep(2000)
                console.log('RELEASE-2s开始-截图')
                await finishShotCamera()
                break
              case CanonActionEnum.NONE:
                console.log('佳能闪光灯wifi链接失败，不可用状态')
                // 直接结束流程去拍照
                await finishShotCamera()
                break
              default:
                break
            }
          },
        })
      } else {
        // 普通摄像头模式
        finishShotCamera()
      }
    }
  )

  useEffect(() => {
    console.log('cameraCurrentStatus', cameraCurrentStatus)
    if (cameraCurrentStatus === CameraCurrentStatus.success) {
      setCreateType(TaskCreateType.SHOOT)
      setStatus(PhotoStatus.countDown)
      startSecond()
    }
  }, [cameraCurrentStatus])

  console.log('mazeDrawPayOrder', mazeDrawPayOrder)

  /** 重新拍摄按钮禁用 */
  const disableRetryShot = useMemo(() => {
    return remainShotTime <= 0
  }, [remainShotTime])

  /** 作画任务的创建和轮询 */
  const toCreate = async (url: string) => {
    return new Promise(async (resolve: (val: boolean) => void, reject) => {
      try {
        const deviceInfo = await getDefaultDeviceInfo()
        const drawData = {
          event_id: selectedEventDetail?.id,
          theme_id: selectedThemeDetail?.id,
          device_id: deviceInfo?.id,
          image_url: url,
          gender: selectedGender === MirrorSexEnum.MALE ? 1 : 2, // 1男 2女
        }
        const res = await _ajax.post(_api.draw, drawData)
        // 定义错误码映射表
        const ERROR_CODE_MAP = {
          1076: () => t('上传的图片不符合要求'),
          1077: () => t('上传的图片不符合要求'),
          1079: () => t('上传的图片不符合要求'),
          1075: () => t('未知错误，请稍后重试'),
          1080: () => t('作画订单创建失败，请稍后重试'),
        } as const

        const responseCode = res?.data?.code

        // 处理成功响应
        if (responseCode === 200) {
          const baseIds = res?.data?.data?.base_ids
          if (baseIds) {
            navigator(`/result?taskBaseId=${baseIds}`)
            resolve(true)
            return
          }
        }

        // 处理错误响应
        const errorHandler =
          ERROR_CODE_MAP[responseCode as keyof typeof ERROR_CODE_MAP]
        if (errorHandler) {
          reject(errorHandler())
        } else {
          // 处理未知错误码
          const errorMsg: string = res?.data?.msg
            ? `${t('作画订单创建失败，请稍后重试')} ${res?.data?.msg}`
            : t('作画订单创建失败，请稍后重试')
          reject(errorMsg)
        }
      } catch (error) {
        reject(error)
      }
    })
  }
  /** 七牛图片上传 */
  const uploadImage = () => {
    return new Promise(async (resolve: (url: string) => void, reject) => {
      const blob = blobImgRef.current
      if (!blob) return reject('')
      try {
        const imgType = blob.type?.split('/')?.[1]

        const imgFile = new File([blob], `mirror_photo.${imgType}`, {
          type: blob.type,
        })
        const { url } = await uploadQiNiu(
          imgFile,
          undefined,
          OssBucket.TRADE_PROTECT,
          'mirror_take_photos',
          {
            isCompress: false,
          }
        )
        if (url) {
          resolve(url)
        } else {
          reject('')
        }
      } catch (error) {
        console.log('error', error)
        reject(error)
      }
    })
  }

  const handleCreateArtwork = useDebounce(async () => {
    try {
      setCreateLoading(true)

      // 拍摄
      if (createType === TaskCreateType.SHOOT) {
        // 上传过的图片不用重新上传
        if (!photoUrlRef.current) {
          const url = await uploadImage()
          photoUrlRef.current = url
        }
        if (!photoUrlRef.current) {
          throw new Error('图片上传失败,请稍后重试')
        }
        await toCreate(photoUrlRef.current)
      } else {
        // 图片上传
        if (!image) {
          throw new Error('图片上传失败,请稍后重试')
        }
        photoUrlRef.current = image

        await toCreate(image)
      }
    } catch (error) {
      setCreateLoading(false)
      console.log('error', error)
      toast({
        description:
          graphQLErrorMessage(error) ||
          (typeof error === 'string' ? error : '作画任务创建失败，请稍后重试'),
      })
    }
  }, [image, createType, photoUrlRef.current])

  useEffect(() => {
    if (createType === TaskCreateType.SHOOT) {
      console.log(
        'screenOrientation.isLandScape 1111',
        screenOrientation.isLandScape,
        'isPortraitCameraEnable',
        isPortraitCameraEnable,
        'window.innerWidth',
        window.innerWidth,
        'window.innerHeight',
        window.innerHeight,
        'isIPad',
        isIPad(),
        'isPad',
        isPad()
      )
      // 初始化摄像头
      initCamera({
        videoOption:
          isIPad() || isPad()
            ? {
                // 1. 安卓，ios平板，仅有aspectRatio，才能约束video比例；但获取的分辨率会下降
                // 2. 摄像头有陀螺仪，画面会跟随。横屏 约束3/4，竖屏 约束4/3
                aspectRatio: {
                  exact: screenOrientation.isLandScape ? 3 / 4 : 4 / 3,
                },
              }
            : window.innerWidth <= 768
              ? {
                  // 手机端
                  aspectRatio: {
                    exact: 16 / 9,
                  },
                }
              : {
                  // 默认：width，height + aspectRatio方案，在获取最大像素情况下，约束video比例
                  // 横屏 约束3/4，竖屏 约束4/3
                  width:
                    window.innerWidth ||
                    (screenOrientation.isLandScape ? 1920 : 1080),
                  height:
                    window.innerHeight ||
                    (screenOrientation.isLandScape ? 1080 : 1920),
                  ...(isMachine() && {
                    aspectRatio: {
                      exact:
                        isPortraitCameraEnable && isMachine() ? 16 / 9 : 9 / 16,
                    },
                  }),
                },
      })
    } else {
      closeCamera()
    }
    return () => {
      closeCamera()
    }
  }, [createType, screenOrientation.isLandScape, isPortraitCameraEnable])

  return (
    <div
      className={classNames(
        styles.container,
        screenOrientation.isPortrait ? 'flex-col pt-[80px]' : 'justify-center'
      )}
    >
      {/* <div
        className={classNames(
          'absolute z-10 left-40',
          styles.left,
          screenOrientation.isLandScape
            ? 'w-[360px]'
            : 'left-[50%] translate-x-[-50%] w-full'
        )}
      >
        <PostureTip
          multiple={drawPayOrder?.order?.item?.fitNumber === FitNumber.TWO}
        />
      </div> */}
      <div
        className={classNames(
          styles.center,
          screenOrientation.isPortrait && 'mt-0 mx-auto mb-0'
        )}
      >
        {/* <PhotoBorder /> */}
        {createType === TaskCreateType.SHOOT ? (
          <div
            className={classNames(
              'relative w-full h-full flex justify-center items-center'
            )}
          >
            {/** 镜像展示视频 */}
            <video
              id={VIDEO_ID}
              className={classNames(
                'absolute top-0 left-0',
                'w-full h-full',
                'object-cover md:object-contain'
              )}
              style={{ transform: 'scaleX(-1)' }}
              poster="noposter"
              autoPlay
              muted
              playsInline
              webkit-playsinline
            ></video>
            <canvas
              id={CANVAS_ID}
              className="w-full h-full absolute"
              style={{ visibility: 'hidden' }}
            />
            {/* {cameraCurrentStatus === CameraCurrentStatus.success &&
              [PhotoStatus.preview, PhotoStatus.countDown].includes(status) &&
              // 适合展示人像虚线框的高宽比
              realVideoSize.width / realVideoSize.height < 0.8 && (
                <PhotoMask
                  multiple={
                    drawPayOrder?.order?.item?.fitNumber === FitNumber.TWO
                  }
                />
              )} */}

            {/* 视频角色引导 */}
            {taskType === AiTaskType.VIDEO &&
              cameraCurrentStatus === CameraCurrentStatus.success &&
              [PhotoStatus.preview, PhotoStatus.countDown].includes(status) && (
                <RoleMask
                  multiple={
                    drawPayOrder?.order?.item?.fitNumber === FitNumber.TWO
                  }
                  faceUrl={drawPayOrder?.order?.item?.faceUrl}
                />
              )}
          </div>
        ) : (
          <CustomPhotoModule />
        )}
      </div>
      {status === PhotoStatus.countDown && (
        <div
          className={classNames(
            'fixed z-20 top-0 left-0 w-full h-full flex items-center justify-center'
          )}
        >
          <PhotoMask multiple={false} direction={screenOrientation} />
        </div>
      )}
      {status === PhotoStatus.countDown && second > 0 && (
        <div
          className={classNames(
            'absolute z-10 translate-y-[-50%] left-[50%] translate-x-[-50%] text-[2rem] font-bold leading-[36px] maze-primary-text flex flex-col items-center',
            screenOrientation.isPortrait
              ? 'absolute top-[40%] left-1/2 -translate-x-1/2 ipad:top-[50%]'
              : 'top-[50%]'
          )}
        >
          <div className="w-[150px] h-[180px] flex items-center justify-end iphone:w-[9.376rem] iphone:h-[11.25rem]">
            <span className="text-[15rem] leading-[180%] font-light mr-2 iphone:text-[15rem]">
              {second}
            </span>
            {/* <span className="mt-[60px]">{t('秒')}</span> */}
          </div>
          {/* <span className=" opacity-[0.32] mt-6">{t('拍摄倒计时')}</span> */}
        </div>
      )}
      <div
        className={classNames(
          'absolute z-10 bottom-[10vh] left-[50%] translate-x-[-50%]',
          styles.right,
          screenOrientation.isLandScape && 'flex-col w-[760px]'
        )}
      >
        {status === PhotoStatus.preview && (
          <>
            {/* <div
              className={classNames(
                screenOrientation.isPortrait ? 'hidden' : 'my-4 mb-32'
              )}
            >
              <div className="text-[30px] font-bold leading-[36px] maze-primary-text flex flex-col items-center">
                <div className="w-[150px] h-[180px] flex items-center justify-end">
                  <span className="text-[128px] leading-[180%] mr-2 maze-primary-text">
                    {SHOT_COUNT_DOWN}
                  </span>
                  <span className="mt-[60px]">{t('秒')}</span>
                </div>
                <span className="opacity-[0.32] mt-6">{t('拍摄倒计时')}</span>
              </div>
            </div> */}
            <div
              className={classNames(
                screenOrientation.isPortrait && 'flex gap-12'
              )}
            >
              {/* {taskType === AiTaskType.DRAW && (
                <Button
                  size="lg"
                  className={classNames(
                    'mb-12 flex',
                    screenOrientation.isLandScape && 'mx-auto'
                  )}
                  ga-data="updatePicture"
                  onClick={() => {
                    setQrcodeOpen(true)
                  }}
                >
                  <MirrorUpload size={40} className="btn-text-color mr-2" />
                  {t('上传图片')}
                </Button>
              )} */}
              {/* 拍摄按钮，不显示，自动倒计时 */}
              {/* {cameraCurrentStatus === CameraCurrentStatus.success && (
                <Button
                  size="lg"
                  className={classNames(
                    'flex',
                    screenOrientation.isLandScape && 'mx-auto'
                  )}
                  ga-data="startCamera"
                  onClick={() => {
                    setCreateType(TaskCreateType.SHOOT)
                    setStatus(PhotoStatus.countDown)
                    startSecond()
                  }}
                >
                  <MirrorCamera size={40} className="btn-text-color mr-2" />
                  {t('开始拍摄')}
                </Button>
              )} */}
            </div>
          </>
        )}

        {status === PhotoStatus.retry && (
          <div
            className={classNames(
              'mx-auto flex justify-between',
              screenOrientation.isPortrait ? 'flex gap-[50vw]' : 'w-full'
            )}
          >
            {cameraCurrentStatus === CameraCurrentStatus.success && (
              <div>
                <Button
                  size="lg"
                  variant="outline"
                  className={classNames(
                    'w-[12.5rem] h-[12.5rem] rounded-full maze-shoot-btn'
                  )}
                  disabled={disableRetryShot}
                  onClick={() => {
                    if (createLoading) return
                    setCreateType(TaskCreateType.SHOOT)
                    // 摄像头画面重启
                    changeVideoStatus()
                    setStatus(PhotoStatus.countDown)
                    resetSecond(SHOT_COUNT_DOWN)
                    setRemainShotTime(remainShotTime - 1)
                    photoUrlRef.current = ''
                  }}
                >
                  <img
                    src={publicPreLoadSourceObj.shootRetry}
                    alt=""
                    className="mr-2"
                  />
                </Button>
                {/* <div className="text-center maze-primary-text opacity-50 text-base">
                  {t('剩余重拍次数：{{remainShotTime}}次', { remainShotTime })}
                </div> */}
              </div>
            )}

            <Button
              size="lg"
              className={classNames(
                'w-[12.5rem] h-[12.5rem] rounded-full maze-shoot-btn'
              )}
              variant="outline"
              ga-data="startCreate"
              loading={createLoading}
              onClick={() => {
                handleCreateArtwork()
              }}
            >
              {!createLoading && (
                <img
                  src={publicPreLoadSourceObj.shootConfirm}
                  alt=""
                  className="mr-2"
                />
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
export default PhotoHasCamera
